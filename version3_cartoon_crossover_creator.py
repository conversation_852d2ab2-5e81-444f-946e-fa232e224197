#!/usr/bin/env python3
"""
Version 3: Cartoon/Anime Crossover Creator
Creates cartoon/anime style crossover scenarios to avoid copyright issues
"""

import os
from openai import OpenAI
import requests
from typing import List, Dict
import random

class CartoonCrossoverCreatorV3:
    def __init__(self, api_key: str):
        self.client = OpenAI(api_key=api_key)
        
        # Hypothetical crossover scenarios (copyright-free interpretations)
        self.crossover_scenarios = {
            "alien_hero_in_monster_world": [
                "A young hero with alien technology exploring a world full of collectible creatures",
                "An alien shapeshifter learning to train mystical beasts in a fantasy realm",
                "A space traveler with transformation powers meeting legendary creatures",
                "An otherworldly hero discovering a planet where creatures battle for sport",
                "A cosmic guardian learning the art of creature companionship"
            ],
            "ninja_vs_dragon_creatures": [
                "A young ninja warrior facing off against a legendary fire-breathing dragon creature",
                "A martial arts master training alongside powerful elemental beasts",
                "A shadow warrior competing in tournaments with mythical creatures",
                "A ninja apprentice forming bonds with ancient dragon spirits",
                "A stealth fighter protecting a village of magical creatures"
            ],
            "robot_hero_in_magic_school": [
                "A mechanical hero learning spellcasting in a mystical academy",
                "A tech-savvy student bringing gadgets to a world of wands and potions",
                "A robotic apprentice discovering the balance between technology and magic",
                "A cyborg wizard mastering both circuits and spells",
                "An android student navigating magical school politics"
            ],
            "pirate_crew_in_space": [
                "A rubber-powered captain leading a crew through cosmic adventures",
                "A stretchy hero and friends exploring alien planets for treasure",
                "A determined captain fighting space tyrants with unusual powers",
                "A crew of misfits sailing through asteroid fields and nebulae",
                "A adventure-loving team discovering intergalactic mysteries"
            ],
            "time_traveling_students": [
                "Young heroes using time manipulation to save different eras",
                "Students with temporal powers preventing historical disasters",
                "Time-traveling teens meeting their future and past selves",
                "Academy students learning to navigate time paradoxes",
                "Young guardians protecting the timeline from cosmic threats"
            ],
            "elemental_warriors_tournament": [
                "Masters of fire, water, earth, and air competing in epic battles",
                "Elemental benders from different worlds meeting in combat",
                "Young warriors discovering new elemental combinations",
                "Ancient elemental spirits choosing modern champions",
                "A tournament where elements clash in spectacular displays"
            ]
        }
        
        # Sample prompts for testing each scenario type
        self.sample_prompts = [
            """Create an anime-style illustration showing a young hero with alien technology (glowing green watch-like device) standing alongside a large orange dragon creature with flame patterns. The setting is a mystical forest with floating islands in the background. Style: Vibrant anime art, dynamic poses, adventure atmosphere.""",
            
            """Generate a cartoon-style battle scene between a blonde ninja warrior in orange clothing and a large fire-breathing dragon creature. The ninja is using energy techniques while the dragon breathes flames. Setting: Rocky mountain landscape. Style: Dynamic action anime, bright colors, epic confrontation.""",
            
            """Design an anime illustration of a robotic student with glowing blue circuits sitting in a magical classroom filled with floating books, crystal orbs, and mystical symbols. The robot is trying to cast a spell with a wand. Style: Cute anime art, magical academy setting, technology meets magic theme.""",
            
            """Create a space adventure scene showing a crew of diverse cartoon characters on a cosmic ship sailing through a nebula. Include a stretchy-armed captain, a green-haired swordsman, and other unique crew members. Style: Colorful anime adventure art, space pirate theme.""",
            
            """Generate an anime-style image of teenage students with glowing time-manipulation powers standing in front of a swirling temporal portal. Show different time periods visible through the portal. Style: Modern anime art, sci-fi elements, dramatic lighting.""",
            
            """Design a tournament arena scene with four warriors representing different elements - one controlling fire, another water, one earth, and one air. Show them in dynamic combat poses with their elemental powers visible. Style: Epic anime battle art, elemental effects, arena setting."""
        ]

    def generate_crossover_art(self, scenario_type: str, custom_prompt: str = "") -> str:
        """Generate cartoon/anime crossover art"""
        
        if scenario_type not in self.crossover_scenarios:
            raise ValueError(f"Scenario type must be one of: {list(self.crossover_scenarios.keys())}")
        
        # Use custom prompt or select from scenario type
        if custom_prompt:
            base_concept = custom_prompt
        else:
            base_concept = random.choice(self.crossover_scenarios[scenario_type])
        
        # Construct the full prompt with anime/cartoon styling
        full_prompt = f"""Create a high-quality anime/cartoon style illustration of: {base_concept}

Art Style Requirements:
- Anime/cartoon illustration style
- Vibrant, saturated colors
- Dynamic character poses and expressions
- Clean line art with bold outlines
- Detailed character designs with unique features
- Fantasy/adventure atmosphere
- Professional anime art quality

Visual Elements:
- Expressive character faces with large eyes (anime style)
- Dynamic action poses or interesting character interactions
- Detailed backgrounds that support the scene
- Special effects for powers/abilities (glowing, energy, etc.)
- Balanced composition with clear focal points

Mood: Adventurous, exciting, heroic, colorful
Quality: High-detail anime artwork suitable for poster or cover art

Avoid any specific copyrighted character designs - create original interpretations of the concepts."""

        try:
            response = self.client.images.generate(
                model="gpt-image-1",
                prompt=full_prompt,
                size="1024x1024",
                quality="high",
                n=1,
            )
            
            return response.data[0].url
            
        except Exception as e:
            print(f"Error generating crossover art: {e}")
            return None

    def get_scenario_types(self) -> List[str]:
        """Get available crossover scenario types"""
        return list(self.crossover_scenarios.keys())

    def get_scenarios_for_type(self, scenario_type: str) -> List[str]:
        """Get specific scenarios for a given type"""
        return self.crossover_scenarios.get(scenario_type, [])

    def get_all_scenarios(self) -> Dict:
        """Get all available scenarios"""
        return self.crossover_scenarios

    def get_sample_prompts(self) -> List[str]:
        """Get sample prompts for testing"""
        return self.sample_prompts

    def save_image(self, image_url: str, filename: str) -> bool:
        """Save generated image to file"""
        try:
            response = requests.get(image_url)
            response.raise_for_status()
            
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            print(f"Image saved as {filename}")
            return True
            
        except Exception as e:
            print(f"Error saving image: {e}")
            return False

    def generate_random_crossover(self) -> str:
        """Generate a random crossover from any scenario type"""
        scenario_type = random.choice(list(self.crossover_scenarios.keys()))
        return self.generate_crossover_art(scenario_type)

    def create_crossover_series(self, scenario_type: str, count: int = 3) -> List[str]:
        """Create a series of related crossover artworks"""
        if scenario_type not in self.crossover_scenarios:
            raise ValueError(f"Invalid scenario type: {scenario_type}")
        
        scenarios = self.crossover_scenarios[scenario_type]
        selected_scenarios = random.sample(scenarios, min(count, len(scenarios)))
        
        image_urls = []
        for i, scenario in enumerate(selected_scenarios):
            print(f"Generating image {i+1}/{len(selected_scenarios)}: {scenario[:50]}...")
            url = self.generate_crossover_art(scenario_type, scenario)
            if url:
                image_urls.append(url)
        
        return image_urls

def main():
    # Initialize with your OpenAI API key
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("Please set your OPENAI_API_KEY environment variable")
        return
    
    creator = CartoonCrossoverCreatorV3(api_key)
    
    # Display available scenario types
    print("Available crossover scenario types:")
    for i, scenario_type in enumerate(creator.get_scenario_types(), 1):
        print(f"{i}. {scenario_type.replace('_', ' ').title()}")
    
    print("\nSample prompts for testing:")
    for i, prompt in enumerate(creator.get_sample_prompts(), 1):
        print(f"{i}. {prompt[:100]}...")
    
    # Generate a sample crossover
    print("\nGenerating sample crossover artwork...")
    image_url = creator.generate_crossover_art(
        "alien_hero_in_monster_world",
        "A young hero with a glowing transformation device meeting a friendly electric mouse creature in a vibrant forest clearing, both looking excited for adventure"
    )
    
    if image_url:
        print(f"Generated crossover art URL: {image_url}")
        creator.save_image(image_url, "crossover_art_v3.png")
    
    # Generate a series example
    print("\nGenerating a series of ninja vs dragon scenarios...")
    series_urls = creator.create_crossover_series("ninja_vs_dragon_creatures", 2)
    
    for i, url in enumerate(series_urls):
        creator.save_image(url, f"crossover_series_{i+1}.png")

if __name__ == "__main__":
    main()

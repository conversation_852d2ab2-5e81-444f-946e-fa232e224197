# Comic War - AI-Powered Meme & Comic Creator

Three different versions of comic/meme creators using GPT-image-1 and other AI models.

## Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set up environment variables:
```bash
export OPENAI_API_KEY="your_openai_api_key"
export GEMINI_API_KEY="your_gemini_api_key"  # Only needed for version 2
```

## Version 1: Broad Topic Meme Creator

Creates memes and comics based on copyright-free topics like animals, space, food, etc.

### Features:
- 6 broad topic categories with 5+ ideas each
- System prompt optimized for meme/comic generation
- 5 sample prompts for testing
- Copyright-safe content generation

### Usage:
```python
python version1_meme_creator.py
```

### Sample Topics:
- **Animals**: Cats being dramatic, dogs vs technology, penguin business meetings
- **Space**: Confused aliens, astronaut problems, planet personalities  
- **Food**: Vegetable rebellions, pizza topping arguments, coffee existential crisis
- **Technology**: Anxious smartphones, computer vacation requests, printer world domination
- **Weather**: Moody clouds, racing raindrops, wind salon chaos
- **Everyday Life**: Enthusiastic alarms, sock disappearances, hiding keys

## Version 2: Video-to-Meme Creator with AI Rating

Creates 4-strip memes based on video scenes and uses Gemini to rate accuracy.

### Features:
- Converts video scene descriptions to 4-panel comics
- Avoids copyright issues by using generic interpretations
- Gemini AI rates how well the comic captures the original scene
- Sample Interstellar-inspired scene included

### Usage:
```python
python version2_video_meme_creator.py
```

### Sample Scene:
Emotional space drama about time dilation and missed family moments, converted to copyright-free 4-panel comic.

## Version 3: Cartoon/Anime Crossover Creator

Creates anime-style crossover scenarios between different fictional universes.

### Features:
- 6 crossover scenario types with multiple variations
- High-quality anime/cartoon art style
- Copyright-safe interpretations of popular concepts
- Series generation capability

### Usage:
```python
python version3_cartoon_crossover_creator.py
```

### Sample Crossover Scenarios:
- **Alien Hero in Monster World**: Tech-powered heroes meeting creature companions
- **Ninja vs Dragon Creatures**: Martial arts masters with mythical beasts
- **Robot Hero in Magic School**: Technology meets mystical education
- **Pirate Crew in Space**: Adventure crews exploring cosmic seas
- **Time Traveling Students**: Temporal powers in academic settings
- **Elemental Warriors Tournament**: Masters of fire, water, earth, air competing

## Sample Prompts for Testing

### Version 1 Examples:
1. "Cat dramatically reacting to empty food bowl like end of world"
2. "Alien tourist confused by human customs, tipping trees"
3. "Vegetables staging refrigerator rebellion with carrot leader"
4. "Smartphone with social anxiety hiding at device party"
5. "Clouds with different personalities arguing about weather"

### Version 2 Example:
"4-panel comic: Astronaut watching messages → realizing time passed → missing life events → crying over lost time"

### Version 3 Examples:
1. "Young hero with alien tech meeting orange dragon creature in mystical forest"
2. "Blonde ninja vs fire-breathing dragon in mountain battle"
3. "Robot student casting spells in magical classroom"
4. "Space pirate crew sailing through cosmic nebula"
5. "Time-manipulating students with temporal portal"
6. "Four elemental warriors in tournament arena"

## Notes

- All scripts avoid copyrighted characters and content
- Images are saved locally when generated
- Version 2 requires both OpenAI and Gemini API keys
- High-quality outputs optimized for meme/comic formats
- Family-friendly, universally relatable content

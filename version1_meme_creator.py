#!/usr/bin/env python3
"""
Version 1: Broad Topic Meme/Comic Creator
Creates memes and comics based on broad, copyright-free topics using GPT-image-1
"""

import os
from openai import OpenAI
import base64
import requests
import random

# Initialize OpenAI client
client = OpenAI()

# System prompt for meme generation
system_prompt = """You are a creative meme and comic generator. Create humorous, engaging visual content based on the given topic.
Focus on:
- Clear, simple visual storytelling
- Relatable humor that works across cultures
- Bright, engaging colors and compositions
- Avoiding any copyrighted characters, brands, or specific references
- Making content that would work well as a meme or comic strip
- Using universal themes and situations

Style guidelines:
- Cartoon/illustration style works best
- Clear, bold text if needed
- Simple backgrounds that don't distract
- Expressive characters and situations"""

# Broad topic categories without copyright issues
topic_categories = {
    "animals": [
        "cats being dramatic about everyday situations",
        "dogs trying to understand human technology",
        "penguins in formal business meetings",
        "squirrels planning elaborate acorn heists",
        "owls giving life advice at 3am"
    ],
    "space": [
        "aliens confused by human social media",
        "astronauts dealing with zero gravity problems",
        "planets having personality conflicts",
        "space tourists complaining about cosmic wifi",
        "robots trying to understand human emotions"
    ],
    "food": [
        "vegetables staging a rebellion in the fridge",
        "pizza slices arguing about toppings",
        "coffee beans having an existential crisis",
        "fruits competing in a beauty contest",
        "kitchen utensils forming a union"
    ],
    "technology": [
        "smartphones having social anxiety",
        "computers trying to take vacation days",
        "wifi signals playing hide and seek",
        "batteries having energy management issues",
        "printers plotting world domination"
    ],
    "weather": [
        "clouds having mood swings",
        "rain drops racing to the ground",
        "snow flakes with unique personality disorders",
        "wind causing chaos in a hair salon",
        "sun and moon arguing about work schedules"
    ],
    "everyday_life": [
        "alarm clocks being overly enthusiastic",
        "socks disappearing in the laundry dimension",
        "keys hiding in impossible places",
        "elevators with social anxiety",
        "traffic lights having power struggles"
    ]
}

# Sample prompts for testing
sample_prompts = [
    "Create a 4-panel comic strip showing a cat dramatically reacting to an empty food bowl, with the cat treating it like the end of the world, complete with theatrical poses and exaggerated expressions",

    "Generate a meme showing an alien tourist taking selfies on Earth but being confused by human customs, like trying to tip a tree or asking a fire hydrant for directions",

    "Design a comic strip of vegetables in a refrigerator staging a rebellion against being eaten, with a carrot as the leader giving a motivational speech to lettuce and tomatoes",

    "Create a humorous image of a smartphone having social anxiety at a party of other devices, hiding behind a tablet while a smartwatch tries to introduce them",

    "Make a 4-panel comic showing clouds having different personalities - one being grumpy and raining, another being cheerful and sunny, and them arguing about the weather forecast"
]

def generate_meme(topic_category, custom_prompt=""):
    """Generate a meme based on topic category and optional custom prompt"""

    if topic_category not in topic_categories:
        print(f"Topic category must be one of: {list(topic_categories.keys())}")
        return None

    # Use custom prompt or select from category
    if custom_prompt:
        base_idea = custom_prompt
    else:
        base_idea = random.choice(topic_categories[topic_category])

    # Construct the full prompt
    full_prompt = f"""Create a humorous meme or comic strip about: {base_idea}

Style: Cartoon illustration, bright colors, clear and simple composition
Format: Single panel meme or 4-panel comic strip
Mood: Funny, relatable, family-friendly
Visual elements: Bold, expressive characters with clear emotions
Text: Minimal but impactful if needed

Make it visually engaging and immediately funny to viewers."""

    try:
        result = client.images.generate(
            model="gpt-image-1",
            prompt=full_prompt,
            size="1024x1024",
            background="transparent",
            quality="high",
        )

        image_base64 = result.json()["data"][0]["b64_json"]
        return image_base64

    except Exception as e:
        print(f"Error generating image: {e}")
        return None

def save_image(image_url, filename):
    """Save generated image to file"""
    try:
        response = requests.get(image_url)
        response.raise_for_status()

        with open(filename, 'wb') as f:
            f.write(response.content)

        print(f"Image saved as {filename}")
        return True

    except Exception as e:
        print(f"Error saving image: {e}")
        return False

# Example usage
print("Available topic categories:")
for category in topic_categories:
    print(f"- {category}")

print("\nSample prompts for testing:")
for i, prompt in enumerate(sample_prompts, 1):
    print(f"{i}. {prompt[:100]}...")

# Generate a sample meme
print("\nGenerating sample meme...")
image_url = generate_meme("animals", "A cat sitting at a computer, wearing glasses, looking very serious while typing an angry email to the dog next door about excessive barking")

if image_url:
    print(f"Generated image URL: {image_url}")
    save_image(image_url, "sample_meme_v1.png")

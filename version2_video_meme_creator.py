#!/usr/bin/env python3
"""
Version 2: Video-to-Meme Creator with Gemini Rating
Creates 4-strip memes based on video scenes and uses Gemini to rate accuracy
"""

import os
import base64
from openai import OpenAI
import google.generativeai as genai
import requests
from typing import List, Dict, Tuple

class VideoMemeCreatorV2:
    def __init__(self, openai_api_key: str, gemini_api_key: str):
        self.openai_client = OpenAI(api_key=openai_api_key)
        genai.configure(api_key=gemini_api_key)
        self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
        
        # Sample video description for Interstellar crying scene
        self.sample_video_description = """
        Scene Description: A dramatic moment where a character discovers they've missed decades of their daughter's life due to time dilation. 
        The character is watching video messages from their daughter aging from child to adult, showing profound grief and regret.
        Visual elements: Close-up of emotional face, tears, video screen showing aging progression, space/sci-fi setting.
        Emotional tone: Heartbreak, loss, time's cruel passage, parental love, sacrifice.
        """
        
        # Sample prompt for the Interstellar-inspired scene (copyright-free version)
        self.sample_scene_prompt = """
        Create a 4-panel comic strip showing:
        Panel 1: An astronaut in a space station looking at a communication device with hope
        Panel 2: The astronaut watching messages, seeing time has passed differently 
        Panel 3: The astronaut realizing they've missed important life events, showing shock
        Panel 4: The astronaut crying while holding the device, overwhelmed by the passage of time
        
        Style: Emotional cartoon illustration, space setting, focus on facial expressions showing the progression from hope to devastating realization.
        No specific movie references, characters, or copyrighted elements.
        """

    def create_video_based_meme(self, scene_description: str, custom_prompt: str = "") -> str:
        """Create a 4-strip meme based on video scene description"""
        
        if custom_prompt:
            prompt = custom_prompt
        else:
            # Generate generic prompt based on scene description
            prompt = f"""Create a 4-panel comic strip that captures the emotional essence of this scene: {scene_description}

Requirements:
- 4 distinct panels showing emotional progression
- Generic characters (no specific people or copyrighted characters)
- Focus on universal human emotions and situations
- Clear visual storytelling without text if possible
- Cartoon/illustration style
- Each panel should build the emotional narrative

Style: Clean cartoon illustration, expressive faces, simple backgrounds, emotional storytelling
Format: 4-panel horizontal comic strip layout
Mood: Dramatic, emotional, relatable"""

        try:
            response = self.openai_client.images.generate(
                model="gpt-image-1",
                prompt=prompt,
                size="1024x1024",
                quality="standard",
                n=1,
            )
            
            return response.data[0].url
            
        except Exception as e:
            print(f"Error generating meme: {e}")
            return None

    def rate_meme_accuracy(self, meme_image_url: str, original_scene_description: str) -> Dict:
        """Use Gemini to rate how well the meme captures the original scene"""
        
        try:
            # Download the generated meme image
            response = requests.get(meme_image_url)
            response.raise_for_status()
            
            # Convert to base64 for Gemini
            image_data = base64.b64encode(response.content).decode('utf-8')
            
            rating_prompt = f"""
            Please analyze this comic/meme image and rate how well it captures the emotional essence and key elements of this scene description:

            Original Scene: {original_scene_description}

            Rate the following aspects on a scale of 1-10:
            1. Emotional accuracy - Does it capture the right emotions?
            2. Visual storytelling - Is the story clear without words?
            3. Composition - Is it well-structured as a comic?
            4. Relatability - Would viewers understand the emotion?
            5. Overall effectiveness - How well does it work as a meme/comic?

            Provide specific feedback on what works well and what could be improved.
            Format your response as JSON with ratings and detailed feedback.
            """

            # Create the image part for Gemini
            image_part = {
                "mime_type": "image/png",
                "data": image_data
            }
            
            response = self.gemini_model.generate_content([rating_prompt, image_part])
            
            return {
                "rating_text": response.text,
                "success": True
            }
            
        except Exception as e:
            print(f"Error rating meme: {e}")
            return {
                "rating_text": f"Error occurred during rating: {e}",
                "success": False
            }

    def get_sample_scene(self) -> str:
        """Get the sample video scene description"""
        return self.sample_video_description

    def get_sample_prompt(self) -> str:
        """Get the sample prompt for the scene"""
        return self.sample_scene_prompt

    def save_image(self, image_url: str, filename: str) -> bool:
        """Save generated image to file"""
        try:
            response = requests.get(image_url)
            response.raise_for_status()
            
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            print(f"Image saved as {filename}")
            return True
            
        except Exception as e:
            print(f"Error saving image: {e}")
            return False

    def create_and_rate_meme(self, scene_description: str, custom_prompt: str = "") -> Dict:
        """Complete workflow: create meme and get Gemini rating"""
        
        print("Creating meme...")
        meme_url = self.create_video_based_meme(scene_description, custom_prompt)
        
        if not meme_url:
            return {"error": "Failed to generate meme"}
        
        print("Rating meme accuracy...")
        rating = self.rate_meme_accuracy(meme_url, scene_description)
        
        return {
            "meme_url": meme_url,
            "rating": rating,
            "scene_description": scene_description
        }

def main():
    # Initialize with API keys
    openai_key = os.getenv("OPENAI_API_KEY")
    gemini_key = os.getenv("GEMINI_API_KEY")
    
    if not openai_key or not gemini_key:
        print("Please set your OPENAI_API_KEY and GEMINI_API_KEY environment variables")
        return
    
    creator = VideoMemeCreatorV2(openai_key, gemini_key)
    
    # Show sample scene and prompt
    print("Sample Video Scene Description:")
    print(creator.get_sample_scene())
    print("\nSample Prompt:")
    print(creator.get_sample_prompt())
    
    # Create and rate a meme based on the sample scene
    print("\nCreating and rating meme for sample scene...")
    result = creator.create_and_rate_meme(
        creator.get_sample_scene(),
        creator.get_sample_prompt()
    )
    
    if "error" not in result:
        print(f"\nGenerated meme URL: {result['meme_url']}")
        creator.save_image(result['meme_url'], "video_meme_v2.png")
        
        print("\nGemini Rating:")
        print(result['rating']['rating_text'])
    else:
        print(f"Error: {result['error']}")

if __name__ == "__main__":
    main()
